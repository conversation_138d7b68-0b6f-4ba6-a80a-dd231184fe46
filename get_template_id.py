#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
获取七鱼工单模板ID
"""

import time
import hashlib
import json
import requests

def get_qiyu_templates():
    """获取七鱼工单模板列表"""
    
    # 配置
    app_key = '6b647f0794b7065adc35551577f5939f'
    app_secret = 'D4AE7FD636304DC7BDAD5DAB0CE3B319'
    
    # 请求数据
    request_data = {"status": 1}
    request_json = json.dumps(request_data, separators=(',', ':'))
    
    # 计算请求json的md5
    request_md5 = hashlib.md5(request_json.encode('utf-8')).hexdigest().lower()
    
    # 生成时间戳
    timestamp = int(time.time())
    
    # 计算checksum: SHA1(app_secret + request_md5 + timestamp)
    checksum_string = f"{app_secret}{request_md5}{timestamp}"
    checksum = hashlib.sha1(checksum_string.encode('utf-8')).hexdigest().lower()
    
    # 请求参数
    params = {
        "appKey": app_key,
        "time": timestamp,
        "checksum": checksum
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json; charset=utf-8"
    }
    
    # URL
    url = "https://qiyukf.com/openapi/v2/ticket/template/list"
    
    try:
        response = requests.post(
            url,
            params=params,
            headers=headers,
            data=request_json,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                # 解析模板列表
                templates = json.loads(result.get("message", "[]"))
                return templates
            else:
                print(f"API错误: {result}")
                return None
        else:
            print(f"HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"请求异常: {e}")
        return None

def find_template_by_name(templates, template_name):
    """根据模板名称查找模板ID"""
    
    if not templates:
        return None
        
    for template in templates:
        if template.get("name") == template_name:
            return template
    
    return None

def main():
    """主函数"""
    
    print("获取七鱼工单模板ID")
    print("=" * 40)
    
    # 获取模板列表
    templates = get_qiyu_templates()
    
    if templates:
        print(f"✅ 成功获取 {len(templates)} 个工单模板")
        
        # 查找"质检预警工单"模板
        target_template = find_template_by_name(templates, "质检预警工单")
        
        if target_template:
            template_id = target_template.get("id")
            template_name = target_template.get("name")
            template_status = target_template.get("status")
            create_time = target_template.get("createTime")
            
            print(f"\n🎯 找到目标模板:")
            print(f"模板ID: {template_id}")
            print(f"模板名称: {template_name}")
            print(f"模板状态: {template_status} (1=启用)")
            print(f"创建时间: {create_time}")
            
            print(f"\n📋 创建工单时使用的模板ID: {template_id}")
            
        else:
            print(f"\n❌ 未找到名为'质检预警工单'的模板")
            print(f"\n可用的模板列表:")
            for i, template in enumerate(templates, 1):
                print(f"{i:2d}. ID: {template.get('id'):8} | 名称: {template.get('name')}")
    
    else:
        print("❌ 获取模板列表失败")

if __name__ == "__main__":
    main()