#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试工单内容优化后的效果
"""

from qiyu_ticket_service import QiyuTicketService
from datetime import datetime

def test_ticket_content():
    """测试工单内容生成"""
    
    # 创建服务实例
    service = QiyuTicketService()
    
    # 模拟分析数据
    analysis_data = {
        '用户诉求': {
            '用户核心诉求': '用户反映交易延迟问题，要求立即处理',
            '佐证': '用户提供了交易截图和时间记录'
        },
        '对话模式': {
            '会话开始时间': '2025-01-15 09:30:00',
            '会话结束时间': '2025-01-15 10:15:00',
            '会话持续时长': '45',
            '对话轮次': '12',
            '客服处理效率': '中等'
        },
        '风险点识别': {
            '投诉风险': '高',
            '投诉类型': '服务质量投诉',
            '投诉渠道': '官方客服热线'
        }
    }
    
    # 测试参数
    session_id = "TEST_SESSION_12345"
    alert_reason = "客服响应时间过长"
    staff_name = "张三"
    customer_name = "李四"
    rule_level = "二级"
    member_id = "MEMBER_67890"
    staff_id = 12345
    
    print("=== 工单内容优化测试 ===")
    print(f"会话ID: {session_id}")
    print(f"告警原因: {alert_reason}")
    print(f"告警等级: {rule_level}")
    print(f"客服: {staff_name}")
    print(f"客户: {customer_name}")
    print()
    
    # 模拟工单内容生成（不实际创建工单）
    priority_map = {
        "一级": 10,  # 非常紧急
        "二级": 8,   # 紧急
        "三级": 5    # 一般
    }
    priority = priority_map.get(rule_level, 5)
    
    # 构建工单标题
    title = f"[{rule_level}告警] {alert_reason} - 会话{session_id}"
    
    # 构建工单内容 - 优化后的版本
    content_parts = [
        f"告警时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"客户: {customer_name}",
        f"会员ID: {member_id or '未知'}",
        "",
        "此工单为系统自动创建的质检预警工单，详细信息请查看下方自定义字段。",
        "",
        "如需处理此告警，请联系相关负责人员。"
    ]
    
    content = "\n".join(content_parts)
    
    # 构建自定义字段
    custom_fields = []
    
    # 会话ID (字段ID: 6063372)
    custom_fields.append({
        "id": 6063372,
        "value": str(session_id)
    })
    
    # 告警类型 (字段ID: 6063366)
    custom_fields.append({
        "id": 6063366,
        "value": "会话质检预警"
    })
    
    # 会话客服 (字段ID: 6063365)
    custom_fields.append({
        "id": 6063365,
        "value": str(staff_name)
    })
    
    # 告警原因 (字段ID: 6063367)
    custom_fields.append({
        "id": 6063367,
        "value": str(alert_reason)
    })
    
    # 用户诉求 (字段ID: 6063368)
    user_demand = analysis_data.get('用户诉求', {})
    user_demand_parts = []
    if user_demand.get('用户核心诉求'):
        user_demand_parts.append(f"核心诉求: {user_demand['用户核心诉求']}")
    if user_demand.get('佐证'):
        user_demand_parts.append(f"佐证: {user_demand['佐证']}")
    user_demand_text = "\n".join(user_demand_parts) if user_demand_parts else ""
    custom_fields.append({
        "id": 6063368,
        "value": user_demand_text
    })
    
    # 会话基本信息 (字段ID: 6063369)
    dialogue_mode = analysis_data.get('对话模式', {})
    basic_info_parts = []
    if dialogue_mode.get('会话开始时间'):
        basic_info_parts.append(f"会话开始时间: {dialogue_mode['会话开始时间']}")
    if dialogue_mode.get('会话结束时间'):
        basic_info_parts.append(f"会话结束时间: {dialogue_mode['会话结束时间']}")
    if dialogue_mode.get('会话持续时长'):
        basic_info_parts.append(f"会话持续时长: {dialogue_mode['会话持续时长']} 分钟")
    basic_info_text = "\n".join(basic_info_parts) if basic_info_parts else ""
    custom_fields.append({
        "id": 6063369,
        "value": basic_info_text
    })
    
    # 对话模式 (字段ID: 6063370)
    dialogue_pattern_parts = []
    if dialogue_mode.get('对话轮次'):
        dialogue_pattern_parts.append(f"对话轮次: {dialogue_mode['对话轮次']}")
    if dialogue_mode.get('客服处理效率'):
        dialogue_pattern_parts.append(f"客服效率: {dialogue_mode['客服处理效率']}")
    dialogue_pattern_text = "\n".join(dialogue_pattern_parts) if dialogue_pattern_parts else ""
    custom_fields.append({
        "id": 6063370,
        "value": dialogue_pattern_text
    })
    
    # 风险点识别 (字段ID: 6063371)
    risk_info = analysis_data.get('风险点识别', {})
    risk_parts = []
    if risk_info.get('投诉风险'):
        risk_parts.append(f"投诉风险: {risk_info['投诉风险']}")
    if risk_info.get('投诉类型'):
        risk_parts.append(f"投诉类型: {risk_info['投诉类型']}")
    if risk_info.get('投诉渠道'):
        risk_parts.append(f"投诉渠道: {risk_info['投诉渠道']}")
    risk_text = "\n".join(risk_parts) if risk_parts else ""
    custom_fields.append({
        "id": 6063371,
        "value": risk_text
    })
    
    # 显示结果
    print("🎯 优化后的工单信息:")
    print("=" * 60)
    print(f"工单标题: {title}")
    print(f"优先级: {priority}")
    print()
    print("📝 工单内容:")
    print("-" * 40)
    print(content)
    print()
    print("🔧 自定义字段:")
    print("-" * 40)
    for i, field in enumerate(custom_fields, 1):
        field_names = {
            6063372: "会话ID",
            6063366: "告警类型", 
            6063365: "会话客服",
            6063367: "告警原因",
            6063368: "用户诉求",
            6063369: "会话基本信息",
            6063370: "对话模式",
            6063371: "风险点识别"
        }
        field_name = field_names.get(field["id"], f"字段{field['id']}")
        print(f"{i:2d}. {field_name}: {field['value']}")
    
    print()
    print("✅ 优化说明:")
    print("- 工单内容中移除了与自定义字段重复的信息")
    print("- 保留了基本的告警时间、客户信息等关键信息")
    print("- 详细的分析数据通过自定义字段结构化展示")
    print("- 避免了信息重复，提高了工单的可读性")

if __name__ == "__main__":
    test_ticket_content()
